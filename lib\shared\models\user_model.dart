/// 👤 نموذج المستخدم
/// User Model for Moon Memory Admin
class UserModel {
  final String id;
  final String nationalId;
  final String fullName;
  final String? email;
  final String? phone;
  final bool isActive;
  final bool isAdmin;
  final String accountType;
  final int maxDevices;
  final int storageQuotaMb;
  final String? department;
  final String? position;
  final String? notes;
  final DateTime? lastLogin;
  final DateTime? passwordChangedAt;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? createdBy;

  const UserModel({
    required this.id,
    required this.nationalId,
    required this.fullName,
    this.email,
    this.phone,
    required this.isActive,
    required this.isAdmin,
    required this.accountType,
    required this.maxDevices,
    required this.storageQuotaMb,
    this.department,
    this.position,
    this.notes,
    this.lastLogin,
    this.passwordChangedAt,
    required this.createdAt,
    this.updatedAt,
    this.createdBy,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      nationalId: json['national_id'] as String,
      fullName: json['full_name'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      isAdmin: json['is_admin'] as bool? ?? false,
      accountType: json['account_type'] as String? ?? 'user',
      maxDevices: json['max_devices'] as int? ?? 3,
      storageQuotaMb: json['storage_quota_mb'] as int? ?? 1000,
      department: json['department'] as String?,
      position: json['position'] as String?,
      notes: json['notes'] as String?,
      lastLogin: json['last_login'] != null 
          ? DateTime.parse(json['last_login'] as String)
          : null,
      passwordChangedAt: json['password_changed_at'] != null 
          ? DateTime.parse(json['password_changed_at'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      createdBy: json['created_by'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'national_id': nationalId,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'is_active': isActive,
      'is_admin': isAdmin,
      'account_type': accountType,
      'max_devices': maxDevices,
      'storage_quota_mb': storageQuotaMb,
      'department': department,
      'position': position,
      'notes': notes,
      'last_login': lastLogin?.toIso8601String(),
      'password_changed_at': passwordChangedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'created_by': createdBy,
    };
  }

  UserModel copyWith({
    String? id,
    String? nationalId,
    String? fullName,
    String? email,
    String? phone,
    bool? isActive,
    bool? isAdmin,
    String? accountType,
    int? maxDevices,
    int? storageQuotaMb,
    String? department,
    String? position,
    String? notes,
    DateTime? lastLogin,
    DateTime? passwordChangedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return UserModel(
      id: id ?? this.id,
      nationalId: nationalId ?? this.nationalId,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      isActive: isActive ?? this.isActive,
      isAdmin: isAdmin ?? this.isAdmin,
      accountType: accountType ?? this.accountType,
      maxDevices: maxDevices ?? this.maxDevices,
      storageQuotaMb: storageQuotaMb ?? this.storageQuotaMb,
      department: department ?? this.department,
      position: position ?? this.position,
      notes: notes ?? this.notes,
      lastLogin: lastLogin ?? this.lastLogin,
      passwordChangedAt: passwordChangedAt ?? this.passwordChangedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  @override
  String toString() {
    return 'UserModel(id: $id, fullName: $fullName, email: $email, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// الحصول على حالة المستخدم كنص
  String get statusText {
    if (!isActive) return 'غير نشط';
    if (isAdmin) return 'مدير';
    return 'نشط';
  }

  /// الحصول على لون حالة المستخدم
  String get statusColor {
    if (!isActive) return '#EF4444'; // Red
    if (isAdmin) return '#8B5CF6'; // Purple
    return '#10B981'; // Green
  }

  /// التحقق من انتهاء صلاحية كلمة المرور
  bool get isPasswordExpired {
    if (passwordChangedAt == null) return true;
    final daysSinceChange = DateTime.now().difference(passwordChangedAt!).inDays;
    return daysSinceChange > 90; // 90 يوم
  }

  /// التحقق من آخر تسجيل دخول
  bool get isRecentlyActive {
    if (lastLogin == null) return false;
    final daysSinceLogin = DateTime.now().difference(lastLogin!).inDays;
    return daysSinceLogin <= 7; // خلال أسبوع
  }
}
