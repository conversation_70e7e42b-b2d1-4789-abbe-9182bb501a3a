import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/dashboard/dashboard_screen.dart';
import '../../features/users/users_screen.dart';
import '../../features/devices/devices_screen.dart';
import '../../features/photos/photos_screen.dart';
import '../../features/reports/reports_screen.dart';
import '../../shared/widgets/main_layout.dart';

/// 🧭 نظام التوجيه الأساسي
/// Main App Router for Moon Memory Admin
class AppRouter {
  
  // 🎯 Route Names
  static const String dashboard = '/';
  static const String users = '/users';
  static const String devices = '/devices';
  static const String photos = '/photos';
  static const String reports = '/reports';
  
  // 🧭 Router Configuration
  static final GoRouter router = GoRouter(
    initialLocation: dashboard,
    debugLogDiagnostics: true,
    routes: [
      ShellRoute(
        builder: (context, state, child) {
          return MainLayout(child: child);
        },
        routes: [
          // 📊 Dashboard Route
          GoRoute(
            path: dashboard,
            name: 'dashboard',
            pageBuilder: (context, state) => _buildPage(
              context,
              state,
              const DashboardScreen(),
              'لوحة التحكم',
            ),
          ),
          
          // 👥 Users Route
          GoRoute(
            path: users,
            name: 'users',
            pageBuilder: (context, state) => _buildPage(
              context,
              state,
              const UsersScreen(),
              'إدارة المستخدمين',
            ),
          ),
          
          // 📱 Devices Route
          GoRoute(
            path: devices,
            name: 'devices',
            pageBuilder: (context, state) => _buildPage(
              context,
              state,
              const DevicesScreen(),
              'إدارة الأجهزة',
            ),
          ),
          
          // 🖼️ Photos Route
          GoRoute(
            path: photos,
            name: 'photos',
            pageBuilder: (context, state) => _buildPage(
              context,
              state,
              const PhotosScreen(),
              'إدارة الصور',
            ),
          ),
          
          // 📈 Reports Route
          GoRoute(
            path: reports,
            name: 'reports',
            pageBuilder: (context, state) => _buildPage(
              context,
              state,
              const ReportsScreen(),
              'التقارير والإحصائيات',
            ),
          ),
        ],
      ),
    ],
    
    // ❌ Error Handler
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في التنقل',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'الصفحة المطلوبة غير موجودة',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(dashboard),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );
  
  // 🏗️ Build Page Helper
  static Page<void> _buildPage(
    BuildContext context,
    GoRouterState state,
    Widget child,
    String title,
  ) {
    return CustomTransitionPage<void>(
      key: state.pageKey,
      child: child,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return SlideTransition(
          position: animation.drive(
            Tween(begin: const Offset(0.1, 0), end: Offset.zero).chain(
              CurveTween(curve: Curves.easeInOut),
            ),
          ),
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }
  
  // 🎯 Navigation Helpers
  
  /// الانتقال إلى لوحة التحكم
  static void goToDashboard(BuildContext context) {
    context.go(dashboard);
  }
  
  /// الانتقال إلى إدارة المستخدمين
  static void goToUsers(BuildContext context) {
    context.go(users);
  }
  
  /// الانتقال إلى إدارة الأجهزة
  static void goToDevices(BuildContext context) {
    context.go(devices);
  }
  
  /// الانتقال إلى إدارة الصور
  static void goToPhotos(BuildContext context) {
    context.go(photos);
  }
  
  /// الانتقال إلى التقارير
  static void goToReports(BuildContext context) {
    context.go(reports);
  }
  
  /// الحصول على المسار الحالي
  static String getCurrentPath(BuildContext context) {
    return GoRouterState.of(context).location;
  }
  
  /// التحقق من المسار النشط
  static bool isCurrentPath(BuildContext context, String path) {
    return getCurrentPath(context) == path;
  }
}

/// 🎨 Navigation Item Model
class NavigationItem {
  final String path;
  final String title;
  final IconData icon;
  final IconData? activeIcon;
  final String? badge;
  final bool isActive;
  
  const NavigationItem({
    required this.path,
    required this.title,
    required this.icon,
    this.activeIcon,
    this.badge,
    this.isActive = false,
  });
  
  NavigationItem copyWith({
    String? path,
    String? title,
    IconData? icon,
    IconData? activeIcon,
    String? badge,
    bool? isActive,
  }) {
    return NavigationItem(
      path: path ?? this.path,
      title: title ?? this.title,
      icon: icon ?? this.icon,
      activeIcon: activeIcon ?? this.activeIcon,
      badge: badge ?? this.badge,
      isActive: isActive ?? this.isActive,
    );
  }
}

/// 📱 Navigation Items Configuration
class NavigationConfig {
  static List<NavigationItem> getNavigationItems(BuildContext context) {
    final currentPath = AppRouter.getCurrentPath(context);
    
    return [
      NavigationItem(
        path: AppRouter.dashboard,
        title: 'لوحة التحكم',
        icon: Icons.dashboard_outlined,
        activeIcon: Icons.dashboard,
        isActive: currentPath == AppRouter.dashboard,
      ),
      NavigationItem(
        path: AppRouter.users,
        title: 'المستخدمون',
        icon: Icons.people_outline,
        activeIcon: Icons.people,
        isActive: currentPath == AppRouter.users,
      ),
      NavigationItem(
        path: AppRouter.devices,
        title: 'الأجهزة',
        icon: Icons.devices_outlined,
        activeIcon: Icons.devices,
        isActive: currentPath == AppRouter.devices,
      ),
      NavigationItem(
        path: AppRouter.photos,
        title: 'الصور',
        icon: Icons.photo_library_outlined,
        activeIcon: Icons.photo_library,
        isActive: currentPath == AppRouter.photos,
      ),
      NavigationItem(
        path: AppRouter.reports,
        title: 'التقارير',
        icon: Icons.analytics_outlined,
        activeIcon: Icons.analytics,
        isActive: currentPath == AppRouter.reports,
      ),
    ];
  }
}
