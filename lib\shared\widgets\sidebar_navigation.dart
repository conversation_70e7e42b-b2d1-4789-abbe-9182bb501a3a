import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../theme/app_colors.dart';
import '../../core/routing/app_router.dart';
import '../../core/constants/app_constants.dart';

/// 🧭 القائمة الجانبية للتنقل
/// Sidebar Navigation for Moon Memory Admin
class SidebarNavigation extends StatefulWidget {
  final bool isExpanded;
  final Animation<double>? animation;
  final bool isMobile;
  
  const SidebarNavigation({
    super.key,
    required this.isExpanded,
    this.animation,
    this.isMobile = false,
  });

  @override
  State<SidebarNavigation> createState() => _SidebarNavigationState();
}

class _SidebarNavigationState extends State<SidebarNavigation> {
  
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // 🌙 App Header
          _buildAppHeader(),
          
          // 📱 Navigation Items
          Expanded(
            child: _buildNavigationItems(),
          ),
          
          // 🔧 Footer
          if (widget.isExpanded) _buildFooter(),
        ],
      ),
    );
  }
  
  // 🌙 App Header
  Widget _buildAppHeader() {
    return Container(
      height: 80,
      padding: const EdgeInsets.all(AppConstants.spacing16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 🌙 App Icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.nights_stay,
              color: AppColors.white,
              size: 24,
            ),
          ),
          
          // 📱 App Title
          if (widget.isExpanded) ...[
            const SizedBox(width: AppConstants.spacing12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'ذاكرة القمر',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  Text(
                    'لوحة التحكم',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  // 📱 Navigation Items
  Widget _buildNavigationItems() {
    final navigationItems = NavigationConfig.getNavigationItems(context);
    
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: AppConstants.spacing8),
      itemCount: navigationItems.length,
      itemBuilder: (context, index) {
        final item = navigationItems[index];
        return _buildNavigationItem(item);
      },
    );
  }
  
  // 🎯 Single Navigation Item
  Widget _buildNavigationItem(NavigationItem item) {
    final isActive = item.isActive;
    
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.spacing8,
        vertical: AppConstants.spacing4,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            context.go(item.path);
            if (widget.isMobile) {
              Navigator.of(context).pop();
            }
          },
          borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.spacing12,
              vertical: AppConstants.spacing12,
            ),
            decoration: BoxDecoration(
              color: isActive 
                  ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
              border: isActive 
                  ? Border.all(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                      width: 1,
                    )
                  : null,
            ),
            child: Row(
              children: [
                // 🎯 Icon
                Icon(
                  isActive ? (item.activeIcon ?? item.icon) : item.icon,
                  color: isActive 
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  size: AppConstants.iconSizeMedium,
                ),
                
                // 📝 Title
                if (widget.isExpanded) ...[
                  const SizedBox(width: AppConstants.spacing12),
                  Expanded(
                    child: Text(
                      item.title,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: isActive 
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.onSurface,
                        fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ),
                ],
                
                // 🏷️ Badge
                if (widget.isExpanded && item.badge != null) ...[
                  const SizedBox(width: AppConstants.spacing8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.error,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      item.badge!,
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  // 🔧 Footer
  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacing16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // 🌙 Theme Toggle
          _buildThemeToggle(),
          
          const SizedBox(height: AppConstants.spacing12),
          
          // ℹ️ Version Info
          Text(
            'الإصدار ${AppConstants.appVersion}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
          ),
        ],
      ),
    );
  }
  
  // 🌙 Theme Toggle
  Widget _buildThemeToggle() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.spacing12,
        vertical: AppConstants.spacing8,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Theme.of(context).brightness == Brightness.dark 
                ? Icons.dark_mode 
                : Icons.light_mode,
            size: AppConstants.iconSizeSmall,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
          const SizedBox(width: AppConstants.spacing8),
          Text(
            Theme.of(context).brightness == Brightness.dark 
                ? 'الوضع المظلم' 
                : 'الوضع المضيء',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }
}
