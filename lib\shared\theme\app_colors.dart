import 'package:flutter/material.dart';

/// 🎨 ألوان التطبيق العصرية
/// Modern App Colors for Moon Memory Admin
class AppColors {
  // 🌙 Primary Colors - ألوان أساسية مستوحاة من القمر
  static const Color primary = Color(0xFF6366F1); // Indigo
  static const Color primaryLight = Color(0xFF818CF8);
  static const Color primaryDark = Color(0xFF4F46E5);
  static const Color primaryContainer = Color(0xFFEEF2FF);
  
  // 🌟 Secondary Colors - ألوان ثانوية
  static const Color secondary = Color(0xFF8B5CF6); // Purple
  static const Color secondaryLight = Color(0xFFA78BFA);
  static const Color secondaryDark = Color(0xFF7C3AED);
  static const Color secondaryContainer = Color(0xFFF3F4F6);
  
  // 🎯 Accent Colors - ألوان مميزة
  static const Color accent = Color(0xFFEC4899); // Pink
  static const Color accentLight = Color(0xFFF472B6);
  static const Color accentDark = Color(0xFFDB2777);
  
  // ✅ Success Colors - ألوان النجاح
  static const Color success = Color(0xFF10B981); // Emerald
  static const Color successLight = Color(0xFF34D399);
  static const Color successDark = Color(0xFF059669);
  static const Color successContainer = Color(0xFFECFDF5);
  
  // ⚠️ Warning Colors - ألوان التحذير
  static const Color warning = Color(0xFFF59E0B); // Amber
  static const Color warningLight = Color(0xFFFBBF24);
  static const Color warningDark = Color(0xFFD97706);
  static const Color warningContainer = Color(0xFFFEF3C7);
  
  // ❌ Error Colors - ألوان الخطأ
  static const Color error = Color(0xFFEF4444); // Red
  static const Color errorLight = Color(0xFFF87171);
  static const Color errorDark = Color(0xFFDC2626);
  static const Color errorContainer = Color(0xFFFEE2E2);
  
  // ℹ️ Info Colors - ألوان المعلومات
  static const Color info = Color(0xFF3B82F6); // Blue
  static const Color infoLight = Color(0xFF60A5FA);
  static const Color infoDark = Color(0xFF2563EB);
  static const Color infoContainer = Color(0xFFDBEAFE);
  
  // 🌈 Chart Colors - ألوان الرسوم البيانية
  static const List<Color> chartColors = [
    Color(0xFF6366F1), // Indigo
    Color(0xFF8B5CF6), // Purple
    Color(0xFFEC4899), // Pink
    Color(0xFFEF4444), // Red
    Color(0xFFF59E0B), // Amber
    Color(0xFF10B981), // Emerald
    Color(0xFF06B6D4), // Cyan
    Color(0xFF3B82F6), // Blue
    Color(0xFF84CC16), // Lime
    Color(0xFFF97316), // Orange
  ];
  
  // 🌅 Gradient Colors - ألوان متدرجة
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    colors: [accent, accentLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient successGradient = LinearGradient(
    colors: [success, successLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // 🌙 Dark Theme Colors - ألوان الوضع المظلم
  static const Color darkBackground = Color(0xFF0F172A); // Slate 900
  static const Color darkSurface = Color(0xFF1E293B); // Slate 800
  static const Color darkCard = Color(0xFF334155); // Slate 700
  static const Color darkBorder = Color(0xFF475569); // Slate 600
  static const Color darkText = Color(0xFFF8FAFC); // Slate 50
  static const Color darkTextSecondary = Color(0xFFCBD5E1); // Slate 300
  
  // ☀️ Light Theme Colors - ألوان الوضع المضيء
  static const Color lightBackground = Color(0xFFFAFAFA); // Gray 50
  static const Color lightSurface = Color(0xFFFFFFFF); // White
  static const Color lightCard = Color(0xFFFFFFFF); // White
  static const Color lightBorder = Color(0xFFE2E8F0); // Slate 200
  static const Color lightText = Color(0xFF0F172A); // Slate 900
  static const Color lightTextSecondary = Color(0xFF64748B); // Slate 500
  
  // 🎨 Neutral Colors - ألوان محايدة
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color transparent = Colors.transparent;
  
  // Gray Scale
  static const Color gray50 = Color(0xFFF9FAFB);
  static const Color gray100 = Color(0xFFF3F4F6);
  static const Color gray200 = Color(0xFFE5E7EB);
  static const Color gray300 = Color(0xFFD1D5DB);
  static const Color gray400 = Color(0xFF9CA3AF);
  static const Color gray500 = Color(0xFF6B7280);
  static const Color gray600 = Color(0xFF4B5563);
  static const Color gray700 = Color(0xFF374151);
  static const Color gray800 = Color(0xFF1F2937);
  static const Color gray900 = Color(0xFF111827);
  
  // 🌟 Special Effects - تأثيرات خاصة
  static const Color shimmerBase = Color(0xFFE2E8F0);
  static const Color shimmerHighlight = Color(0xFFF1F5F9);
  static const Color overlay = Color(0x80000000);
  static const Color divider = Color(0xFFE2E8F0);
  
  // 📊 Status Colors - ألوان الحالات
  static const Color online = Color(0xFF10B981); // Green
  static const Color offline = Color(0xFF6B7280); // Gray
  static const Color pending = Color(0xFFF59E0B); // Amber
  static const Color blocked = Color(0xFFEF4444); // Red
  static const Color approved = Color(0xFF10B981); // Green
  
  // 🎯 Helper Methods - طرق مساعدة
  
  /// الحصول على لون حسب النوع
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'online':
      case 'approved':
      case 'success':
        return success;
      case 'inactive':
      case 'offline':
        return gray500;
      case 'pending':
      case 'warning':
        return warning;
      case 'blocked':
      case 'error':
      case 'failed':
        return error;
      case 'info':
        return info;
      default:
        return gray500;
    }
  }
  
  /// الحصول على لون الرسم البياني حسب الفهرس
  static Color getChartColor(int index) {
    return chartColors[index % chartColors.length];
  }
  
  /// إنشاء لون بشفافية
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
  
  /// تحويل HEX إلى Color
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }
  
  /// تحويل Color إلى HEX
  static String toHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2)}';
  }
}
