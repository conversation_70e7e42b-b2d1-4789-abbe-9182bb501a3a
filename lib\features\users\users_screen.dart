import 'package:flutter/material.dart';
import '../../shared/theme/app_colors.dart';
import '../../core/constants/app_constants.dart';

/// 👥 شاشة إدارة المستخدمين
/// Users Management Screen for Moon Memory Admin
class UsersScreen extends StatefulWidget {
  const UsersScreen({super.key});

  @override
  State<UsersScreen> createState() => _UsersScreenState();
}

class _UsersScreenState extends State<UsersScreen> {
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.spacing24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 📋 Header
            _buildHeader(),
            
            const SizedBox(height: AppConstants.spacing24),
            
            // 🔍 Search and Filters
            _buildSearchAndFilters(),
            
            const SizedBox(height: AppConstants.spacing24),
            
            // 📊 Users Table
            Expanded(
              child: _buildUsersTable(),
            ),
          ],
        ),
      ),
    );
  }
  
  // 📋 Header
  Widget _buildHeader() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '👥 إدارة المستخدمين',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: AppConstants.spacing8),
              Text(
                'عرض وإدارة جميع مستخدمي النظام',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
        ElevatedButton.icon(
          onPressed: () {
            // TODO: Add new user
          },
          icon: const Icon(Icons.add),
          label: const Text('إضافة مستخدم'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
          ),
        ),
      ],
    );
  }
  
  // 🔍 Search and Filters
  Widget _buildSearchAndFilters() {
    return Card(
      elevation: AppConstants.cardElevation,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacing20),
        child: Row(
          children: [
            // 🔍 Search Field
            Expanded(
              flex: 2,
              child: TextField(
                decoration: const InputDecoration(
                  hintText: 'البحث عن مستخدم...',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  // TODO: Implement search
                },
              ),
            ),
            
            const SizedBox(width: AppConstants.spacing16),
            
            // 🎯 Status Filter
            Expanded(
              child: DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'الحالة',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                  DropdownMenuItem(value: 'active', child: Text('نشط')),
                  DropdownMenuItem(value: 'inactive', child: Text('غير نشط')),
                  DropdownMenuItem(value: 'blocked', child: Text('محظور')),
                ],
                onChanged: (value) {
                  // TODO: Implement filter
                },
              ),
            ),
            
            const SizedBox(width: AppConstants.spacing16),
            
            // 📊 Export Button
            OutlinedButton.icon(
              onPressed: () {
                // TODO: Export users
              },
              icon: const Icon(Icons.download),
              label: const Text('تصدير'),
            ),
          ],
        ),
      ),
    );
  }
  
  // 📊 Users Table
  Widget _buildUsersTable() {
    return Card(
      elevation: AppConstants.cardElevation,
      child: Column(
        children: [
          // 📋 Table Header
          Container(
            padding: const EdgeInsets.all(AppConstants.spacing16),
            decoration: BoxDecoration(
              color: AppColors.gray50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppConstants.borderRadius),
                topRight: Radius.circular(AppConstants.borderRadius),
              ),
            ),
            child: Row(
              children: [
                Text(
                  'قائمة المستخدمين (1,234)',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () {
                    // TODO: Refresh data
                  },
                  icon: const Icon(Icons.refresh),
                  tooltip: 'تحديث البيانات',
                ),
              ],
            ),
          ),
          
          // 📊 Table Content
          Expanded(
            child: ListView.builder(
              itemCount: 10, // Mock data
              itemBuilder: (context, index) {
                return _buildUserRow(index);
              },
            ),
          ),
        ],
      ),
    );
  }
  
  // 👤 User Row
  Widget _buildUserRow(int index) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacing16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
          ),
        ),
      ),
      child: Row(
        children: [
          // 👤 Avatar
          CircleAvatar(
            radius: 20,
            backgroundColor: AppColors.chartColors[index % AppColors.chartColors.length],
            child: Text(
              'م${index + 1}',
              style: const TextStyle(
                color: AppColors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          const SizedBox(width: AppConstants.spacing16),
          
          // 📝 User Info
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'محمد أحمد علي ${index + 1}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'user${index + 1}@moonmemory.com',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          
          // 📱 Devices Count
          Expanded(
            child: Column(
              children: [
                Text(
                  '${index % 3 + 1}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                Text(
                  'جهاز',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          
          // 📊 Status
          Expanded(
            child: _buildStatusChip(_getUserStatus(index)),
          ),
          
          // ⏰ Last Login
          Expanded(
            child: Text(
              'منذ ${index + 1} ساعة',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ),
          
          // ⚙️ Actions
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'view',
                child: ListTile(
                  leading: Icon(Icons.visibility),
                  title: Text('عرض التفاصيل'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'edit',
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text('تعديل'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'devices',
                child: ListTile(
                  leading: Icon(Icons.devices),
                  title: Text('إدارة الأجهزة'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem(
                value: 'block',
                child: ListTile(
                  leading: Icon(Icons.block, color: AppColors.error),
                  title: Text('حظر المستخدم', style: TextStyle(color: AppColors.error)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
            onSelected: (value) {
              // TODO: Handle user actions
            },
          ),
        ],
      ),
    );
  }
  
  // 🏷️ Status Chip
  Widget _buildStatusChip(String status) {
    Color color;
    String text;
    
    switch (status) {
      case 'active':
        color = AppColors.success;
        text = 'نشط';
        break;
      case 'inactive':
        color = AppColors.warning;
        text = 'غير نشط';
        break;
      case 'blocked':
        color = AppColors.error;
        text = 'محظور';
        break;
      default:
        color = AppColors.gray500;
        text = 'غير محدد';
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 6,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: color,
          fontWeight: FontWeight.w600,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
  
  // 🎯 Helper Methods
  String _getUserStatus(int index) {
    const statuses = ['active', 'inactive', 'blocked'];
    return statuses[index % statuses.length];
  }
}
