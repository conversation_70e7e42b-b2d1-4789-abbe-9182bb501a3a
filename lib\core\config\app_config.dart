import 'package:flutter_dotenv/flutter_dotenv.dart';

/// 🌙 إعدادات التطبيق الأساسية
/// Moon Memory Admin App Configuration
class AppConfig {
  // 🔗 Supabase Configuration
  static String get supabaseUrl => dotenv.env['SUPABASE_URL'] ?? '';
  static String get supabaseAnonKey => dotenv.env['SUPABASE_ANON_KEY'] ?? '';
  
  // 🎨 App Information
  static String get appName => dotenv.env['APP_NAME'] ?? 'لوحة تحكم ذاكرة القمر';
  static String get appVersion => dotenv.env['APP_VERSION'] ?? '1.0.0';
  static String get environment => dotenv.env['APP_ENVIRONMENT'] ?? 'development';
  
  // 🌍 Localization
  static String get defaultLocale => dotenv.env['DEFAULT_LOCALE'] ?? 'ar';
  static List<String> get supportedLocales => 
      dotenv.env['SUPPORTED_LOCALES']?.split(',') ?? ['ar', 'en'];
  
  // 📊 Dashboard Configuration
  static int get refreshInterval => 
      int.tryParse(dotenv.env['REFRESH_INTERVAL'] ?? '30') ?? 30;
  static int get maxChartDataPoints => 
      int.tryParse(dotenv.env['MAX_CHART_DATA_POINTS'] ?? '100') ?? 100;
  static int get defaultDateRange => 
      int.tryParse(dotenv.env['DEFAULT_DATE_RANGE'] ?? '30') ?? 30;
  
  // 🔔 Features
  static bool get enableNotifications => 
      dotenv.env['ENABLE_NOTIFICATIONS']?.toLowerCase() == 'true';
  static bool get notificationSound => 
      dotenv.env['NOTIFICATION_SOUND']?.toLowerCase() == 'true';
  static bool get enableDarkMode => 
      dotenv.env['ENABLE_DARK_MODE']?.toLowerCase() == 'true';
  static bool get enableAnimations => 
      dotenv.env['ENABLE_ANIMATIONS']?.toLowerCase() == 'true';
  static bool get enableExport => 
      dotenv.env['ENABLE_EXPORT']?.toLowerCase() == 'true';
  
  // ✅ Validation
  static bool get isConfigured => 
      supabaseUrl.isNotEmpty && supabaseAnonKey.isNotEmpty;
  
  // 🎯 Development Mode
  static bool get isDevelopment => environment == 'development';
  static bool get isProduction => environment == 'production';
  
  // 📱 Platform Configuration
  static const int mobileBreakpoint = 768;
  static const int tabletBreakpoint = 1024;
  static const int desktopBreakpoint = 1200;
  
  // 🎨 UI Configuration
  static const double borderRadius = 12.0;
  static const double cardElevation = 2.0;
  static const double animationDuration = 300.0;
  
  // 📊 Chart Colors
  static const List<String> chartColors = [
    '#6366F1', // Indigo
    '#8B5CF6', // Purple
    '#EC4899', // Pink
    '#EF4444', // Red
    '#F59E0B', // Amber
    '#10B981', // Emerald
    '#06B6D4', // Cyan
    '#3B82F6', // Blue
  ];
}
