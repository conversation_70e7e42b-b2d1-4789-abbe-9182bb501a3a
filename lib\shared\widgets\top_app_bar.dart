import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../../core/constants/app_constants.dart';

/// 🔝 شريط التطبيق العلوي
/// Top App Bar for Moon Memory Admin
class TopAppBar extends StatelessWidget implements PreferredSizeWidget {
  final VoidCallback? onMenuPressed;
  final bool isSidebarExpanded;
  final String? title;
  
  const TopAppBar({
    super.key,
    this.onMenuPressed,
    this.isSidebarExpanded = true,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 70,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppConstants.spacing16),
        child: Row(
          children: [
            // 🍔 Menu <PERSON> (Desktop)
            if (onMenuPressed != null) ...[
              IconButton(
                onPressed: onMenuPressed,
                icon: Icon(
                  isSidebarExpanded ? Icons.menu_open : Icons.menu,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                tooltip: isSidebarExpanded ? 'إخفاء القائمة' : 'إظهار القائمة',
              ),
              const SizedBox(width: AppConstants.spacing8),
            ],
            
            // 📱 Mobile Menu Button
            if (onMenuPressed == null) ...[
              Builder(
                builder: (context) => IconButton(
                  onPressed: () => Scaffold.of(context).openDrawer(),
                  icon: Icon(
                    Icons.menu,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  tooltip: 'فتح القائمة',
                ),
              ),
              const SizedBox(width: AppConstants.spacing8),
            ],
            
            // 📝 Page Title
            if (title != null) ...[
              Text(
                title!,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
            
            const Spacer(),
            
            // 🔍 Search Button
            _buildSearchButton(context),
            
            const SizedBox(width: AppConstants.spacing8),
            
            // 🔔 Notifications Button
            _buildNotificationsButton(context),
            
            const SizedBox(width: AppConstants.spacing8),
            
            // 🌙 Theme Toggle Button
            _buildThemeToggleButton(context),
            
            const SizedBox(width: AppConstants.spacing16),
            
            // 👤 User Profile
            _buildUserProfile(context),
          ],
        ),
      ),
    );
  }
  
  // 🔍 Search Button
  Widget _buildSearchButton(BuildContext context) {
    return IconButton(
      onPressed: () {
        // TODO: Implement search functionality
        showSearch(
          context: context,
          delegate: _AppSearchDelegate(),
        );
      },
      icon: Icon(
        Icons.search,
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
      ),
      tooltip: 'البحث',
    );
  }
  
  // 🔔 Notifications Button
  Widget _buildNotificationsButton(BuildContext context) {
    return Stack(
      children: [
        IconButton(
          onPressed: () {
            // TODO: Show notifications panel
            _showNotificationsPanel(context);
          },
          icon: Icon(
            Icons.notifications_outlined,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
          tooltip: 'الإشعارات',
        ),
        
        // 🔴 Notification Badge
        Positioned(
          right: 8,
          top: 8,
          child: Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: AppColors.error,
              shape: BoxShape.circle,
            ),
          ),
        ),
      ],
    );
  }
  
  // 🌙 Theme Toggle Button
  Widget _buildThemeToggleButton(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return IconButton(
      onPressed: () {
        // TODO: Implement theme toggle
        _toggleTheme(context);
      },
      icon: Icon(
        isDark ? Icons.light_mode : Icons.dark_mode,
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
      ),
      tooltip: isDark ? 'الوضع المضيء' : 'الوضع المظلم',
    );
  }
  
  // 👤 User Profile
  Widget _buildUserProfile(BuildContext context) {
    return PopupMenuButton<String>(
      offset: const Offset(0, 50),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.spacing12,
          vertical: AppConstants.spacing8,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 👤 Avatar
            CircleAvatar(
              radius: 16,
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: const Icon(
                Icons.person,
                color: AppColors.white,
                size: 18,
              ),
            ),
            
            const SizedBox(width: AppConstants.spacing8),
            
            // 📝 User Info
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'المدير العام',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                Text(
                  '<EMAIL>',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
            
            const SizedBox(width: AppConstants.spacing8),
            
            // 🔽 Dropdown Arrow
            Icon(
              Icons.keyboard_arrow_down,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              size: 18,
            ),
          ],
        ),
      ),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'profile',
          child: ListTile(
            leading: Icon(Icons.person_outline),
            title: Text('الملف الشخصي'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
        const PopupMenuItem(
          value: 'settings',
          child: ListTile(
            leading: Icon(Icons.settings_outlined),
            title: Text('الإعدادات'),
            contentPadding: EdgeInsets.zero,
          ),
        ),
        const PopupMenuDivider(),
        const PopupMenuItem(
          value: 'logout',
          child: ListTile(
            leading: Icon(Icons.logout, color: AppColors.error),
            title: Text('تسجيل الخروج', style: TextStyle(color: AppColors.error)),
            contentPadding: EdgeInsets.zero,
          ),
        ),
      ],
      onSelected: (value) {
        switch (value) {
          case 'profile':
            // TODO: Show profile
            break;
          case 'settings':
            // TODO: Show settings
            break;
          case 'logout':
            // TODO: Logout
            break;
        }
      },
    );
  }
  
  // 🔔 Show Notifications Panel
  void _showNotificationsPanel(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإشعارات'),
        content: const SizedBox(
          width: 300,
          height: 200,
          child: Center(
            child: Text('لا توجد إشعارات جديدة'),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
  
  // 🌙 Toggle Theme
  void _toggleTheme(BuildContext context) {
    // TODO: Implement theme toggle logic
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تطبيق تبديل الثيم قريباً'),
        duration: Duration(seconds: 2),
      ),
    );
  }
  
  @override
  Size get preferredSize => const Size.fromHeight(70);
}

// 🔍 Search Delegate
class _AppSearchDelegate extends SearchDelegate<String> {
  @override
  String get searchFieldLabel => 'البحث...';
  
  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        onPressed: () => query = '',
        icon: const Icon(Icons.clear),
      ),
    ];
  }
  
  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      onPressed: () => close(context, ''),
      icon: const Icon(Icons.arrow_back),
    );
  }
  
  @override
  Widget buildResults(BuildContext context) {
    return const Center(
      child: Text('نتائج البحث ستظهر هنا'),
    );
  }
  
  @override
  Widget buildSuggestions(BuildContext context) {
    return const Center(
      child: Text('اقتراحات البحث ستظهر هنا'),
    );
  }
}
