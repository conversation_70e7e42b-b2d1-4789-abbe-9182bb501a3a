import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';
import 'sidebar_navigation.dart';
import 'top_app_bar.dart';

/// 🏗️ التخطيط الرئيسي للتطبيق
/// Main Layout for Moon Memory Admin
class MainLayout extends StatefulWidget {
  final Widget child;

  const MainLayout({
    super.key,
    required this.child,
  });

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout>
    with TickerProviderStateMixin {
  
  late AnimationController _sidebarController;
  late Animation<double> _sidebarAnimation;
  bool _isSidebarExpanded = true;
  bool _isMobile = false;
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }
  
  void _initializeAnimations() {
    _sidebarController = AnimationController(
      duration: const Duration(milliseconds: AppConstants.mediumAnimationDuration),
      vsync: this,
    );
    
    _sidebarAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _sidebarController,
      curve: Curves.easeInOut,
    ));
    
    if (_isSidebarExpanded) {
      _sidebarController.forward();
    }
  }
  
  @override
  void dispose() {
    _sidebarController.dispose();
    super.dispose();
  }
  
  void _toggleSidebar() {
    setState(() {
      _isSidebarExpanded = !_isSidebarExpanded;
      if (_isSidebarExpanded) {
        _sidebarController.forward();
      } else {
        _sidebarController.reverse();
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        _isMobile = constraints.maxWidth < AppConstants.mobileBreakpoint;
        
        return Scaffold(
          body: Row(
            children: [
              // 📱 Sidebar Navigation
              if (!_isMobile) _buildDesktopSidebar(),
              
              // 📄 Main Content Area
              Expanded(
                child: Column(
                  children: [
                    // 🔝 Top App Bar
                    TopAppBar(
                      onMenuPressed: _isMobile ? null : _toggleSidebar,
                      isSidebarExpanded: _isSidebarExpanded,
                    ),
                    
                    // 📄 Page Content
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(AppConstants.spacing24),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                        ),
                        child: widget.child,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          // 📱 Mobile Drawer
          drawer: _isMobile ? _buildMobileDrawer() : null,
        );
      },
    );
  }
  
  // 🖥️ Desktop Sidebar
  Widget _buildDesktopSidebar() {
    return AnimatedBuilder(
      animation: _sidebarAnimation,
      builder: (context, child) {
        final width = _isSidebarExpanded 
            ? AppConstants.navigationDrawerWidth 
            : AppConstants.navigationRailWidth;
            
        return Container(
          width: width,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                width: 1,
              ),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(2, 0),
              ),
            ],
          ),
          child: SidebarNavigation(
            isExpanded: _isSidebarExpanded,
            animation: _sidebarAnimation,
          ),
        );
      },
    );
  }
  
  // 📱 Mobile Drawer
  Widget _buildMobileDrawer() {
    return Drawer(
      width: AppConstants.navigationDrawerWidth,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
        ),
        child: const SidebarNavigation(
          isExpanded: true,
          isMobile: true,
        ),
      ),
    );
  }
}

/// 🎨 Layout Helper Functions
class LayoutHelper {
  
  /// التحقق من نوع الجهاز
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < AppConstants.mobileBreakpoint;
  }
  
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= AppConstants.mobileBreakpoint && 
           width < AppConstants.desktopBreakpoint;
  }
  
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= AppConstants.desktopBreakpoint;
  }
  
  /// الحصول على عدد الأعمدة للشبكة
  static int getGridColumns(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < AppConstants.mobileBreakpoint) return 1;
    if (width < AppConstants.tabletBreakpoint) return 2;
    if (width < AppConstants.desktopBreakpoint) return 3;
    return 4;
  }
  
  /// الحصول على المساحة الجانبية
  static EdgeInsets getScreenPadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(AppConstants.spacing16);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(AppConstants.spacing24);
    } else {
      return const EdgeInsets.all(AppConstants.spacing32);
    }
  }
  
  /// الحصول على عرض المحتوى الأقصى
  static double getMaxContentWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1200) {
      return 1200;
    }
    return screenWidth;
  }
  
  /// الحصول على ارتفاع الكارت
  static double getCardHeight(BuildContext context) {
    if (isMobile(context)) return 120;
    if (isTablet(context)) return 140;
    return 160;
  }
}
