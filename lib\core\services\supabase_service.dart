import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import '../config/app_config.dart';

/// 🌐 خدمة Supabase الأساسية
/// Core Supabase Service for Moon Memory Admin
class SupabaseService {
  static final Logger _logger = Logger();
  static SupabaseClient? _client;
  
  /// الحصول على عميل Supabase
  static SupabaseClient get client {
    if (_client == null) {
      throw Exception('Supabase client not initialized. Call initialize() first.');
    }
    return _client!;
  }
  
  /// تهيئة Supabase
  static Future<void> initialize() async {
    try {
      if (!AppConfig.isConfigured) {
        throw Exception('Supabase configuration is missing. Please check your .env file.');
      }
      
      await Supabase.initialize(
        url: AppConfig.supabaseUrl,
        anonKey: AppConfig.supabaseAnonKey,
        debug: AppConfig.isDevelopment,
      );
      
      _client = Supabase.instance.client;
      _logger.i('✅ Supabase initialized successfully');
      
    } catch (e) {
      _logger.e('❌ Failed to initialize Supabase: $e');
      rethrow;
    }
  }
  
  /// التحقق من حالة الاتصال
  static bool get isInitialized => _client != null;
  
  /// الحصول على معلومات المستخدمين
  static Future<List<Map<String, dynamic>>> getUsers({
    int? limit,
    int? offset,
    String? searchQuery,
  }) async {
    try {
      var query = client.from('users').select('''
        id,
        national_id,
        full_name,
        email,
        phone,
        is_active,
        is_admin,
        account_type,
        max_devices,
        storage_quota_mb,
        department,
        position,
        last_login,
        created_at,
        updated_at
      ''');
      
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or('full_name.ilike.%$searchQuery%,email.ilike.%$searchQuery%,national_id.ilike.%$searchQuery%');
      }
      
      if (limit != null) {
        query = query.limit(limit);
      }
      
      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 10) - 1);
      }
      
      final response = await query.order('created_at', ascending: false);
      return List<Map<String, dynamic>>.from(response);
      
    } catch (e) {
      _logger.e('❌ Error fetching users: $e');
      rethrow;
    }
  }
  
  /// الحصول على معلومات الأجهزة
  static Future<List<Map<String, dynamic>>> getDevices({
    int? limit,
    int? offset,
    String? userId,
  }) async {
    try {
      var query = client.from('devices').select('''
        id,
        user_id,
        device_fingerprint,
        android_id,
        device_name,
        device_model,
        device_brand,
        device_product,
        os_version,
        app_version,
        is_active,
        is_approved,
        last_seen,
        created_at,
        users!inner(full_name, email)
      ''');
      
      if (userId != null) {
        query = query.eq('user_id', userId);
      }
      
      if (limit != null) {
        query = query.limit(limit);
      }
      
      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 10) - 1);
      }
      
      final response = await query.order('last_seen', ascending: false);
      return List<Map<String, dynamic>>.from(response);
      
    } catch (e) {
      _logger.e('❌ Error fetching devices: $e');
      rethrow;
    }
  }
  
  /// الحصول على معلومات الصور
  static Future<List<Map<String, dynamic>>> getPhotos({
    int? limit,
    int? offset,
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      var query = client.from('photos').select('''
        id,
        user_id,
        storage_path,
        image_url,
        location,
        latitude,
        longitude,
        file_size,
        image_width,
        image_height,
        date_time,
        created_at,
        users!inner(full_name, email)
      ''');
      
      if (userId != null) {
        query = query.eq('user_id', userId);
      }
      
      if (startDate != null) {
        query = query.gte('date_time', startDate.toIso8601String());
      }
      
      if (endDate != null) {
        query = query.lte('date_time', endDate.toIso8601String());
      }
      
      if (limit != null) {
        query = query.limit(limit);
      }
      
      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 10) - 1);
      }
      
      final response = await query.order('date_time', ascending: false);
      return List<Map<String, dynamic>>.from(response);
      
    } catch (e) {
      _logger.e('❌ Error fetching photos: $e');
      rethrow;
    }
  }
  
  /// الحصول على الإحصائيات العامة
  static Future<Map<String, dynamic>> getStatistics() async {
    try {
      // إحصائيات المستخدمين
      final usersCount = await client.from('users').select('id', const FetchOptions(count: CountOption.exact));
      final activeUsersCount = await client.from('users')
          .select('id', const FetchOptions(count: CountOption.exact))
          .eq('is_active', true);
      
      // إحصائيات الأجهزة
      final devicesCount = await client.from('devices').select('id', const FetchOptions(count: CountOption.exact));
      final activeDevicesCount = await client.from('devices')
          .select('id', const FetchOptions(count: CountOption.exact))
          .eq('is_active', true);
      
      // إحصائيات الصور
      final photosCount = await client.from('photos').select('id', const FetchOptions(count: CountOption.exact));
      
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final photosToday = await client.from('photos')
          .select('id', const FetchOptions(count: CountOption.exact))
          .gte('date_time', startOfDay.toIso8601String());
      
      // حساب مساحة التخزين
      final storageQuery = await client.from('photos')
          .select('file_size')
          .not('file_size', 'is', null);
      
      int totalStorageUsed = 0;
      for (final photo in storageQuery) {
        totalStorageUsed += (photo['file_size'] as int? ?? 0);
      }
      
      return {
        'total_users': usersCount.count ?? 0,
        'active_users': activeUsersCount.count ?? 0,
        'total_devices': devicesCount.count ?? 0,
        'active_devices': activeDevicesCount.count ?? 0,
        'total_photos': photosCount.count ?? 0,
        'photos_today': photosToday.count ?? 0,
        'storage_used_bytes': totalStorageUsed,
        'storage_used_mb': (totalStorageUsed / (1024 * 1024)).round(),
      };
      
    } catch (e) {
      _logger.e('❌ Error fetching statistics: $e');
      rethrow;
    }
  }
  
  /// تحديث حالة المستخدم
  static Future<void> updateUserStatus(String userId, bool isActive) async {
    try {
      await client.from('users')
          .update({'is_active': isActive, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', userId);
      
      _logger.i('✅ User status updated successfully');
    } catch (e) {
      _logger.e('❌ Error updating user status: $e');
      rethrow;
    }
  }
  
  /// تحديث حالة الجهاز
  static Future<void> updateDeviceStatus(String deviceId, bool isActive, bool isApproved) async {
    try {
      await client.from('devices')
          .update({
            'is_active': isActive,
            'is_approved': isApproved,
            'updated_at': DateTime.now().toIso8601String()
          })
          .eq('id', deviceId);
      
      _logger.i('✅ Device status updated successfully');
    } catch (e) {
      _logger.e('❌ Error updating device status: $e');
      rethrow;
    }
  }
}
