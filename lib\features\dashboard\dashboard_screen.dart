import 'package:flutter/material.dart';
import '../../shared/theme/app_colors.dart';
import '../../core/constants/app_constants.dart';

/// 📊 شاشة لوحة التحكم الرئيسية
/// Main Dashboard Screen for Moon Memory Admin
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.spacing24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 🌟 Welcome Header
            _buildWelcomeHeader(),
            
            const SizedBox(height: AppConstants.spacing32),
            
            // 📊 Statistics Cards
            _buildStatisticsCards(),
            
            const SizedBox(height: AppConstants.spacing32),
            
            // 📈 Charts Section
            _buildChartsSection(),
            
            const SizedBox(height: AppConstants.spacing32),
            
            // 📋 Recent Activity
            _buildRecentActivity(),
          ],
        ),
      ),
    );
  }
  
  // 🌟 Welcome Header
  Widget _buildWelcomeHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.spacing24),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '🌙 مرحباً بك في لوحة التحكم',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.spacing8),
                Text(
                  'إدارة شاملة لنظام كاميرا ذاكرة القمر',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.dashboard,
            color: AppColors.white,
            size: 48,
          ),
        ],
      ),
    );
  }
  
  // 📊 Statistics Cards
  Widget _buildStatisticsCards() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: _getGridColumns(),
      crossAxisSpacing: AppConstants.spacing16,
      mainAxisSpacing: AppConstants.spacing16,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          title: 'إجمالي المستخدمين',
          value: '1,234',
          icon: Icons.people,
          color: AppColors.primary,
          trend: '+12%',
        ),
        _buildStatCard(
          title: 'الأجهزة النشطة',
          value: '856',
          icon: Icons.devices,
          color: AppColors.success,
          trend: '+8%',
        ),
        _buildStatCard(
          title: 'الصور اليوم',
          value: '342',
          icon: Icons.photo_camera,
          color: AppColors.warning,
          trend: '+25%',
        ),
        _buildStatCard(
          title: 'مساحة التخزين',
          value: '2.4 GB',
          icon: Icons.storage,
          color: AppColors.info,
          trend: '+5%',
        ),
      ],
    );
  }
  
  // 📊 Single Stat Card
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? trend,
  }) {
    return Card(
      elevation: AppConstants.cardElevation,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacing20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(
                  icon,
                  color: color,
                  size: AppConstants.iconSizeLarge,
                ),
                if (trend != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.success.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      trend,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
            const Spacer(),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: AppConstants.spacing4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // 📈 Charts Section
  Widget _buildChartsSection() {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Card(
            elevation: AppConstants.cardElevation,
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.spacing20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'نشاط المستخدمين',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: AppConstants.spacing16),
                  Container(
                    height: 200,
                    decoration: BoxDecoration(
                      color: AppColors.gray50,
                      borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                    ),
                    child: const Center(
                      child: Text('📈 رسم بياني لنشاط المستخدمين'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: AppConstants.spacing16),
        Expanded(
          child: Card(
            elevation: AppConstants.cardElevation,
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.spacing20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'توزيع الأجهزة',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: AppConstants.spacing16),
                  Container(
                    height: 200,
                    decoration: BoxDecoration(
                      color: AppColors.gray50,
                      borderRadius: BorderRadius.circular(AppConstants.smallBorderRadius),
                    ),
                    child: const Center(
                      child: Text('🥧 رسم دائري للأجهزة'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  // 📋 Recent Activity
  Widget _buildRecentActivity() {
    return Card(
      elevation: AppConstants.cardElevation,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacing20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'النشاط الأخير',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  onPressed: () {},
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.spacing16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 5,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: AppColors.chartColors[index % AppColors.chartColors.length],
                    child: Icon(
                      _getActivityIcon(index),
                      color: AppColors.white,
                      size: 20,
                    ),
                  ),
                  title: Text(_getActivityTitle(index)),
                  subtitle: Text(_getActivityTime(index)),
                  trailing: Icon(
                    Icons.chevron_right,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  // 🎯 Helper Methods
  int _getGridColumns() {
    final width = MediaQuery.of(context).size.width;
    if (width < 768) return 1;
    if (width < 1024) return 2;
    return 4;
  }
  
  IconData _getActivityIcon(int index) {
    const icons = [
      Icons.person_add,
      Icons.photo_camera,
      Icons.devices,
      Icons.security,
      Icons.analytics,
    ];
    return icons[index % icons.length];
  }
  
  String _getActivityTitle(int index) {
    const titles = [
      'مستخدم جديد انضم للنظام',
      'تم رفع 15 صورة جديدة',
      'جهاز جديد في انتظار الموافقة',
      'تحديث إعدادات الأمان',
      'تم إنتاج تقرير شهري',
    ];
    return titles[index % titles.length];
  }
  
  String _getActivityTime(int index) {
    const times = [
      'منذ 5 دقائق',
      'منذ 15 دقيقة',
      'منذ ساعة',
      'منذ ساعتين',
      'منذ 3 ساعات',
    ];
    return times[index % times.length];
  }
}
