import 'package:flutter/material.dart';
import 'core/routing/app_router.dart';
import 'shared/theme/app_theme.dart';

/// 🌙 نقطة البداية الرئيسية لتطبيق لوحة التحكم
/// Main Entry Point for Moon Memory Admin
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 🚀 تشغيل التطبيق
  runApp(const MoonMemoryAdminApp());
}

/// 🌙 التطبيق الرئيسي
/// Main Moon Memory Admin App
class MoonMemoryAdminApp extends StatelessWidget {
  const MoonMemoryAdminApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      // 🎨 App Configuration
      title: 'لوحة تحكم ذاكرة القمر',
      debugShowCheckedModeBanner: false,

      // 🎨 Theme
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,

      // 🧭 Routing
      routerConfig: AppRouter.router,

      // 🏗️ Builder for RTL Support
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: child ?? const SizedBox(),
        );
      },
    );
  }
}
