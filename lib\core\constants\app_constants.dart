/// 🎯 ثوابت التطبيق
/// App Constants for Moon Memory Admin
class AppConstants {
  
  // 📱 App Information
  static const String appName = 'لوحة تحكم ذاكرة القمر';
  static const String appNameEn = 'Moon Memory Admin';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'لوحة تحكم إدارية عصرية لنظام كاميرا ذاكرة القمر';
  
  // 🌐 API & Database
  static const int apiTimeout = 30; // seconds
  static const int maxRetries = 3;
  static const int pageSize = 20;
  static const int maxPageSize = 100;
  
  // 📊 Dashboard
  static const int defaultRefreshInterval = 30; // seconds
  static const int maxChartDataPoints = 100;
  static const int defaultDateRange = 30; // days
  static const int maxDateRange = 365; // days
  
  // 📱 Responsive Breakpoints
  static const double mobileBreakpoint = 768;
  static const double tabletBreakpoint = 1024;
  static const double desktopBreakpoint = 1200;
  static const double largeDesktopBreakpoint = 1440;
  
  // 🎨 UI Dimensions
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  static const double cardElevation = 2.0;
  static const double modalElevation = 8.0;
  
  // ⏱️ Animation Durations
  static const int shortAnimationDuration = 200; // milliseconds
  static const int mediumAnimationDuration = 300; // milliseconds
  static const int longAnimationDuration = 500; // milliseconds
  
  // 📏 Spacing
  static const double spacing4 = 4.0;
  static const double spacing8 = 8.0;
  static const double spacing12 = 12.0;
  static const double spacing16 = 16.0;
  static const double spacing20 = 20.0;
  static const double spacing24 = 24.0;
  static const double spacing32 = 32.0;
  static const double spacing48 = 48.0;
  static const double spacing64 = 64.0;
  
  // 📊 Chart Configuration
  static const double chartHeight = 300.0;
  static const double smallChartHeight = 200.0;
  static const double largeChartHeight = 400.0;
  
  // 📋 Table Configuration
  static const double tableRowHeight = 56.0;
  static const double tableHeaderHeight = 48.0;
  static const int maxTableRows = 50;
  
  // 🖼️ Image Configuration
  static const double avatarSize = 40.0;
  static const double smallAvatarSize = 32.0;
  static const double largeAvatarSize = 64.0;
  static const double thumbnailSize = 80.0;
  static const double previewSize = 200.0;
  
  // 📁 File Configuration
  static const int maxFileSize = 10 * 1024 * 1024; // 10 MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx'];
  
  // 🔔 Notification Configuration
  static const int maxNotifications = 50;
  static const int notificationDisplayDuration = 5; // seconds
  
  // 🔍 Search Configuration
  static const int minSearchLength = 2;
  static const int searchDebounceMs = 500;
  static const int maxSearchResults = 100;
  
  // 📊 Statistics Configuration
  static const List<String> statisticsPeriods = [
    'today',
    'yesterday',
    'last_7_days',
    'last_30_days',
    'last_90_days',
    'last_year',
    'all_time'
  ];
  
  // 🎯 Status Types
  static const List<String> userStatuses = [
    'active',
    'inactive',
    'blocked',
    'pending'
  ];
  
  static const List<String> deviceStatuses = [
    'active',
    'inactive',
    'pending_approval',
    'blocked'
  ];
  
  static const List<String> photoStatuses = [
    'uploaded',
    'processing',
    'approved',
    'rejected'
  ];
  
  // 🌍 Localization
  static const List<String> supportedLanguages = ['ar', 'en'];
  static const String defaultLanguage = 'ar';
  
  // 📱 Platform Configuration
  static const List<String> supportedPlatforms = [
    'android',
    'ios',
    'web',
    'windows',
    'macos',
    'linux'
  ];
  
  // 🎨 Theme Configuration
  static const List<String> themeOptions = [
    'light',
    'dark',
    'system'
  ];
  
  // 📊 Export Formats
  static const List<String> exportFormats = [
    'excel',
    'csv',
    'pdf',
    'json'
  ];
  
  // 🔐 Security Configuration
  static const int maxLoginAttempts = 5;
  static const int lockoutDurationMinutes = 30;
  static const int sessionTimeoutMinutes = 60;
  static const int passwordMinLength = 8;
  
  // 📈 Performance Configuration
  static const int cacheExpirationHours = 24;
  static const int maxCacheSize = 100; // MB
  static const int imageCompressionQuality = 85;
  
  // 🎯 Feature Flags
  static const Map<String, bool> featureFlags = {
    'enableDarkMode': true,
    'enableAnimations': true,
    'enableNotifications': true,
    'enableExport': true,
    'enableAdvancedFilters': true,
    'enableRealTimeUpdates': true,
    'enableOfflineMode': false,
    'enableBetaFeatures': false,
  };
  
  // 📊 Default Values
  static const Map<String, dynamic> defaultValues = {
    'userStorageQuotaMB': 1000,
    'userMaxDevices': 3,
    'deviceSessionTimeoutMinutes': 60,
    'photoCompressionQuality': 85,
    'chartRefreshIntervalSeconds': 30,
    'tablePageSize': 20,
    'searchResultsLimit': 50,
  };
  
  // 🎨 Icon Sizes
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;
  
  // 📱 Navigation
  static const double navigationRailWidth = 72.0;
  static const double navigationDrawerWidth = 280.0;
  static const double bottomNavigationHeight = 80.0;
  
  // 🎯 Validation Rules
  static const Map<String, dynamic> validationRules = {
    'emailRegex': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    'phoneRegex': r'^[+]?[0-9]{10,15}$',
    'nationalIdRegex': r'^[0-9]{10}$',
    'passwordMinLength': 8,
    'nameMinLength': 2,
    'nameMaxLength': 50,
  };
}
